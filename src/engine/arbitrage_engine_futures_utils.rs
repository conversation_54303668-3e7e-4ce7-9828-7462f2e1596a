use crate::{
    encoding::futures_order::OrderSide,
    engine::{
        futures_const::FUTURES_PRICE_TICK_SIZE,
        opportunity_simulator::{Opportunity, OpportunityStatus, OpportunityType, PredictResult},
    },
    utils::perf::system_now_in_us,
};

pub fn generate_take_profit_and_stop_loss_opportunity(
    price: f64,
    origin_side: OrderSide,
    origin_opportunity_id: u64,
) -> [Opportunity; 2] {
    let take_profit_id = system_now_in_us();
    let stop_loss_id = take_profit_id + 1;
    let take_profit_price = match origin_side {
        OrderSide::Buy => price + 6.0 * FUTURES_PRICE_TICK_SIZE,
        OrderSide::Sell => price - 6.0 * FUTURES_PRICE_TICK_SIZE,
    };
    let stop_loss_price = match origin_side {
        OrderSide::Buy => price - 3.0 * FUTURES_PRICE_TICK_SIZE,
        OrderSide::Sell => price + 3.0 * FUTURES_PRICE_TICK_SIZE,
    };
    [
        Opportunity {
            id: take_profit_id,
            origin_order_id: origin_opportunity_id,
            price: take_profit_price,
            side: origin_side.reverse(),
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::TakeProfit,
        },
        Opportunity {
            id: stop_loss_id,
            origin_order_id: origin_opportunity_id,
            price: stop_loss_price,
            side: origin_side.reverse(),
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::StopLoss,
        },
    ]
}
