/*
TODO:
1. 订阅1000档行情
2. 计算每30秒的avg trade vol
3. 挂载avg trade vol 内
 */

use crate::{
    Result, WebSocketHandle,
    encoding::{
        agg_trades::FuturesAggTrade,
        book_ticker::FuturesBookTicker,
        futures_order::{
            OrderSide, generate_futures_cancel_order_request, generate_futures_order_request,
            generate_futures_reduce_only_request,
        },
        futures_order_response::OrderTradeUpdate,
        futures_orderbook::{FuturesOrderbookSnapshot, FuturesOrderbookUpdate},
    },
    engine::{
        futures_agg_trades_aggregator::FuturesAggTradeAggregator,
        futures_const::{FUTURES_IN_LEN, FUTURES_OUT_LEN, FUTURES_PRICE_TICK_SIZE, FUTURES_SYMBOL},
        futures_orderbook::{FuturesOrderBook, ImbalanceType},
        futures_position::FuturesPosition,
        opportunity_simulator::{
            Opportunity, OpportunitySimulator, OpportunityStatus, OpportunityType, PredictResult,
        },
        token::ORDER_TOKEN_1,
    },
    utils::perf::system_now_in_us,
};

pub struct ArbitrageEngineFutures {
    pub orderbook: FuturesOrderBook<20>,
    pub agg_traders: FuturesAggTradeAggregator<50>, // 使用const泛型，最大50个聚合数据
    pub last_opportunity: Option<Opportunity>,
    pub position: FuturesPosition,
    pub buy_order_ids: Vec<(u64, f64)>,
    pub sell_order_ids: Vec<(u64, f64)>,
    pub take_profit_order_ids: Vec<u64>,
    pub opportunity_simulator: OpportunitySimulator,
}

impl ArbitrageEngineFutures {
    pub fn new() -> Self {
        Self {
            orderbook: FuturesOrderBook::new(),
            agg_traders: FuturesAggTradeAggregator::new(), // 不再需要参数
            last_opportunity: None,
            position: FuturesPosition::new(),
            buy_order_ids: Vec::new(),
            sell_order_ids: Vec::new(),
            take_profit_order_ids: Vec::new(),
            opportunity_simulator: OpportunitySimulator::new(),
        }
    }

    fn update_price(&mut self) {
        let best_bid = self.orderbook.best_bid().unwrap().price;
        let best_ask = self.orderbook.best_ask().unwrap().price;
        self.position.update_current_price(best_bid, best_ask);
        self.opportunity_simulator.update_price(best_bid, best_ask);
    }

    pub fn update_orderbook_snapshot(&mut self, depth_snapshot: &FuturesOrderbookSnapshot) -> bool {
        if self.orderbook.apply_snapshot(depth_snapshot) {
            self.update_price();
            true
        } else {
            false
        }
    }

    pub fn update_orderbook_diff(&mut self, depth_diff: &FuturesOrderbookUpdate) -> bool {
        if self.orderbook.apply_diff(depth_diff) {
            self.update_price();
            true
        } else {
            false
        }
    }

    pub fn update_book_ticker(&mut self, book_ticker: &FuturesBookTicker) -> bool {
        if self.orderbook.apply_book_ticker(book_ticker) {
            self.update_price();
            true
        } else {
            false
        }
    }

    pub fn try_take_profit_or_stop_loss(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        match self.position.try_close_position() {
            Some((price, position)) => self.close_position(handle, price, position),
            None => Ok(()),
        }
    }

    pub fn add_agg_trades(&mut self, agg_trade: &FuturesAggTrade) {
        self.agg_traders.add_trades(agg_trade);
    }

    pub fn try_agg_trades_arbitrage(&mut self) -> Option<([f64; 2], OrderSide)> {
        if let Some(last_bar) = self.agg_traders.get_last_bar() {
            if last_bar.total_volume < 30_000.0 {
                return None;
            }
            if last_bar.buy_volume > last_bar.sell_volume * 1.5 && last_bar.open < last_bar.close {
                let last_trade = self.agg_traders.last_trade.as_ref().unwrap();
                let side = OrderSide::Buy;
                let price = [last_trade.price, last_trade.price + FUTURES_PRICE_TICK_SIZE];
                let opportunity = Opportunity {
                    id: system_now_in_us(),
                    origin_order_id: 0,
                    price: price[0],
                    side,
                    status: OpportunityStatus::Pending,
                    predict_result: PredictResult::None,
                    opportunity_type: OpportunityType::LargeTrade,
                };
                self.last_opportunity = Some(opportunity.clone());
                crate::info!(
                    "Large trade oppurtunity: side: {} price: {}",
                    side.to_string(),
                    price[0]
                );
                self.opportunity_simulator.add_opportunity(opportunity);
            }
            if last_bar.sell_volume > last_bar.buy_volume * 2.0 && last_bar.open > last_bar.close {
                let last_trade = self.agg_traders.last_trade.as_ref().unwrap();
                let side = OrderSide::Sell;
                let price = [last_trade.price, last_trade.price - FUTURES_PRICE_TICK_SIZE];
                let opportunity = Opportunity {
                    id: system_now_in_us(),
                    origin_order_id: 0,
                    price: price[0],
                    side,
                    status: OpportunityStatus::Pending,
                    predict_result: PredictResult::None,
                    opportunity_type: OpportunityType::LargeTrade,
                };
                self.last_opportunity = Some(opportunity.clone());
                crate::info!(
                    "Large trade oppurtunity: side: {} price: {}",
                    side.to_string(),
                    price[0]
                );
                self.opportunity_simulator.add_opportunity(opportunity);
            }

            // return Some((prices, side));
        }
        None
    }

    pub fn try_orderbook_arbitrage(&mut self) -> Option<([f64; 2], OrderSide)> {
        let (_, order_side) = match self.orderbook.is_imbalance() {
            ImbalanceType::BidMore(total_ask_qty) => (total_ask_qty, OrderSide::Buy),
            ImbalanceType::AskMore(total_bid_qty) => (total_bid_qty, OrderSide::Sell),
            ImbalanceType::Balanced => return None,
        };
        let agg_trades_features = match self.agg_traders.get_agg_features(30) {
            Some(features) => features,
            None => {
                crate::info!("get_agg_features is none");
                return None;
            }
        };
        let vol_surge_rate = agg_trades_features.vol_surge_rate;
        if vol_surge_rate > 100.0 {
            crate::info!("vol_surge_rate: {}", vol_surge_rate);
            return None;
        }
        let price_open_close_avg_change_rate = agg_trades_features.price_open_close_avg_change_rate;
        if price_open_close_avg_change_rate.abs() > 0.0002 {
            crate::info!(
                "price_open_close_avg_change_rate: {}",
                price_open_close_avg_change_rate
            );
            return None;
        }
        let price_high_low_avg_change_rate = agg_trades_features.price_high_low_avg_change_rate;
        if price_high_low_avg_change_rate.abs() > 0.0002 {
            crate::info!(
                "price_high_low_avg_change_rate: {}",
                price_high_low_avg_change_rate
            );
            return None;
        }
        let order_price = match order_side {
            OrderSide::Buy => {
                let base = self.orderbook.best_ask().unwrap().price;
                [base, base + FUTURES_PRICE_TICK_SIZE]
            }
            OrderSide::Sell => {
                let base = self.orderbook.best_bid().unwrap().price;
                [base, base - FUTURES_PRICE_TICK_SIZE]
            }
        };
        if let Some(ref last_opportunity) = self.last_opportunity {
            if last_opportunity.price == order_price[0] && last_opportunity.side == order_side {
                return None;
            }
        }
        let opportunity = Opportunity {
            id: system_now_in_us(),
            origin_order_id: 0,
            price: order_price[0],
            side: order_side,
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::OrderbookImbalance,
        };
        crate::info!(
            "Orderbook imbalance oppurtunity: side: {} price: {}",
            order_side.to_string(),
            order_price[0]
        );
        self.last_opportunity = Some(opportunity.clone());
        self.opportunity_simulator.add_opportunity(opportunity);
        // Some((order_price, order_side))
        None
    }

    pub fn place_order(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        prices: [f64; 2],
        side: OrderSide,
    ) -> Result<()> {
        self.cancel_order(handle, side)?;

        for price in prices {
            let order_id = system_now_in_us();
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_order_request(price, side, FUTURES_SYMBOL, order_id),
            )?;
            if side == OrderSide::Buy {
                self.buy_order_ids.push((order_id, price));
            } else {
                self.sell_order_ids.push((order_id, price));
            }
        }
        Ok(())
    }

    pub fn close_position(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        price: f64,
        position: f64,
    ) -> Result<()> {
        let side = if position > 0.0 {
            OrderSide::Sell
        } else {
            OrderSide::Buy
        };
        let position = position.abs();
        let order = generate_futures_reduce_only_request(price, side, position, FUTURES_SYMBOL);
        handle.send_message(ORDER_TOKEN_1, order)
    }

    pub fn remove_order_by_id(&mut self, order_id: u64) {
        self.buy_order_ids.retain(|(id, _)| *id != order_id);
        self.sell_order_ids.retain(|(id, _)| *id != order_id);
        self.take_profit_order_ids.retain(|id| *id != order_id);
    }

    pub fn cancel_order(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        side: OrderSide,
    ) -> Result<()> {
        if side == OrderSide::Buy {
            if self.sell_order_ids.len() > 0 {
                crate::info!("cancel sell order: {:?}", self.sell_order_ids);
                let (order_id, _) = self.sell_order_ids.remove(0);
                handle.send_message(
                    ORDER_TOKEN_1,
                    generate_futures_cancel_order_request(order_id),
                )?;
            }
            if self.buy_order_ids.len() > 2 {
                crate::info!("cancel buy order: {:?}", self.buy_order_ids);
                let (order_id, _) = self.buy_order_ids.remove(0);
                handle.send_message(
                    ORDER_TOKEN_1,
                    generate_futures_cancel_order_request(order_id),
                )?;
            }
        } else if side == OrderSide::Sell {
            if self.buy_order_ids.len() > 0 {
                crate::info!("cancel buy order: {:?}", self.buy_order_ids);
                let (order_id, _) = self.buy_order_ids.remove(0);
                handle.send_message(
                    ORDER_TOKEN_1,
                    generate_futures_cancel_order_request(order_id),
                )?;
            }
            if self.sell_order_ids.len() > 2 {
                crate::info!("cancel sell order: {:?}", self.sell_order_ids);
                let (order_id, _) = self.sell_order_ids.remove(0);
                handle.send_message(
                    ORDER_TOKEN_1,
                    generate_futures_cancel_order_request(order_id),
                )?;
            }
        }
        Ok(())
    }

    pub fn cancel_all_orders(
        &mut self,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        let now = system_now_in_us();
        for order_id in self.buy_order_ids.iter() {
            if now - order_id.0 < 1000 * 1000 * 30 {
                continue;
            }
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(order_id.0),
            )?;
        }
        for order_id in self.sell_order_ids.iter() {
            if now - order_id.0 < 1000 * 1000 * 30 {
                continue;
            }
            handle.send_message(
                ORDER_TOKEN_1,
                generate_futures_cancel_order_request(order_id.0),
            )?;
        }
        Ok(())
    }

    pub fn update_trade(
        &mut self,
        trade: &OrderTradeUpdate,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
    ) -> Result<()> {
        self.position.add_trade(trade);
        self.opportunity_simulator.add_trade(trade);
        if self.take_profit_order_ids.contains(&trade.order_id) {
            self.take_profit_order_ids
                .retain(|id| *id != trade.order_id);
            return Ok(());
        }
        if trade.order_side == OrderSide::Buy {
            self.buy_order_ids.retain(|(id, _)| *id != trade.order_id);
        } else {
            self.sell_order_ids.retain(|(id, _)| *id != trade.order_id);
        }
        self.place_reverse_maker_order(trade.order_side, handle, trade.price)
    }

    pub fn place_reverse_maker_order(
        &mut self,
        order_side: OrderSide,
        handle: &mut WebSocketHandle<FUTURES_IN_LEN, FUTURES_OUT_LEN>,
        price: f64,
    ) -> Result<()> {
        let now = system_now_in_us();
        // 这里不能从orderbook里寻找挂单位置，因为orderbook已经过期了
        let (place_order_side, target_price) = if order_side == OrderSide::Buy {
            (OrderSide::Sell, price + 20.0 * FUTURES_PRICE_TICK_SIZE)
        } else {
            (OrderSide::Buy, price - 20.0 * FUTURES_PRICE_TICK_SIZE)
        };
        let opportunity = Opportunity {
            id: now,
            origin_order_id: 0,
            price: target_price,
            side: place_order_side,
            status: OpportunityStatus::Pending,
            predict_result: PredictResult::None,
            opportunity_type: OpportunityType::TakeProfit,
        };
        // self.opportunity_simulator
        //     .add_take_profit_opportunity(opportunity);
        handle.send_message(
            ORDER_TOKEN_1,
            generate_futures_order_request(target_price, place_order_side, FUTURES_SYMBOL, now),
        )?;
        crate::info!(
            "placing instant take profit order: side: {} price: {}",
            order_side.to_string(),
            target_price
        );
        self.take_profit_order_ids.push(now);
        loop {
            if self.take_profit_order_ids.is_empty() {
                break;
            }
            let order_id = self.take_profit_order_ids[0];
            if now - order_id > 1000 * 1000 * 60 * 30 {
                self.take_profit_order_ids.remove(0);
                handle.send_message(
                    ORDER_TOKEN_1,
                    generate_futures_cancel_order_request(order_id),
                )?;
                crate::info!("cancelling take profit order: {}", order_id);
            } else {
                break;
            }
        }
        Ok(())
    }
}
