use crate::{encoding::futures_order_response::OrderTradeUpdate, utils::perf::system_now_in_us};

pub struct FuturesPosition {
    pub position: f64,
    pub entry_price: f64,
    pub unrealized_pnl: f64,
    pub realized_pnl: f64,
    pub current_bid_price: f64,
    pub current_ask_price: f64,
    pub last_close_time: u64,
}

impl FuturesPosition {
    pub fn new() -> Self {
        Self {
            position: 0.0,
            entry_price: 0.0,
            unrealized_pnl: 0.0,
            realized_pnl: 0.0,
            current_bid_price: 0.0,
            current_ask_price: 0.0,
            last_close_time: 0,
        }
    }

    pub fn add_trade(&mut self, trade: &OrderTradeUpdate) {
        crate::info!(
            r#"
Add trade before:
trade price:{:.2}
trade qty: {:.3}
trade side: {}
position: {:.3}
entry price: {:.2}
unrealized pnl: {:.5}
realized pnl: {:.5}
"#,
            trade.price,
            trade.quantity,
            trade.order_side.to_string(),
            self.position,
            self.entry_price,
            self.unrealized_pnl,
            self.realized_pnl,
        );
        let signed_qty = trade.quantity * trade.order_side.sign();
        if self.position < f64::EPSILON {
            self.entry_price = trade.price;
            self.position = signed_qty;
            return;
        }
        if (self.position > 0.0 && signed_qty > 0.0) || (self.position < 0.0 && signed_qty < 0.0) {
            let old_qty = self.position.abs();
            let new_qty = signed_qty.abs();
            let new_total_qty = old_qty + new_qty;
            if new_total_qty > 0.0 {
                self.entry_price =
                    (self.entry_price * old_qty + trade.price * new_qty) / new_total_qty;
            }
            self.position += signed_qty;
            return;
        }
        let pos_sign = self.position.signum(); // +1 for long, -1 for short
        let close_qty = f64::min(self.position.abs(), signed_qty.abs());
        let leg_pnl = match pos_sign {
            x if x > 0.0 => (trade.price - self.entry_price) * close_qty,
            _ => (self.entry_price - trade.price) * close_qty,
        };
        self.realized_pnl += leg_pnl;
        let remaining = self.position.abs() - close_qty;
        if remaining > f64::EPSILON {
            // Still same side after partial close
            self.position = pos_sign * remaining;
            // avg_entry_price unchanged in WAP after partial close
        } else {
            // Fully closed; maybe reverse if trade qty exceeds position
            let overshoot = signed_qty.abs() - close_qty;
            if overshoot > f64::EPSILON {
                // Reverse: open new position on opposite side at trade price
                let new_sign = -pos_sign; // flipped side
                self.position = new_sign * overshoot;
                self.entry_price = trade.price;
            } else {
                // Flat
                self.position = 0.0;
                self.entry_price = 0.0;
            }
        }
        crate::info!(
            r#"
Add trade after:
trade price:{}
trade qty: {}
trade side: {}
position: {}
entry price: {}
unrealized pnl: {}
realized pnl: {}
"#,
            trade.price,
            trade.quantity,
            trade.order_side.to_string(),
            self.position,
            self.entry_price,
            self.unrealized_pnl,
            self.realized_pnl,
        );
    }

    pub fn update_current_price(&mut self, bid_price: f64, ask_price: f64) {
        self.current_bid_price = bid_price;
        self.current_ask_price = ask_price;
        if self.position < f64::EPSILON {
            return;
        }
        let price = if self.position > 0.0 {
            ask_price
        } else {
            bid_price
        };
        self.unrealized_pnl = match self.position.signum() {
            1.0 => (price - self.entry_price) * self.position,
            -1.0 => (self.entry_price - price) * -self.position,
            _ => 0.0,
        };
    }

    pub fn try_close_position(&mut self) -> Option<(f64, f64)> {
        let now = system_now_in_us();
        if now - self.last_close_time < 1000 * 1000 {
            return None;
        }
        if self.position.abs() <= 0.001 {
            return None;
        }
        let price = if self.position > 0.0 {
            self.current_ask_price - 0.001
        } else {
            self.current_bid_price + 0.001
        };
        let taker_fee = self.position.abs() * price * 0.0004;
        if self.unrealized_pnl > taker_fee * 30.0 {
            crate::info!(
                "take profit: price: {}, position: {}, amout: {}, unrealized pnl: {}",
                price,
                self.position,
                self.position * price,
                self.unrealized_pnl
            );
            self.last_close_time = now;

            Some((price, self.position))
        } else if self.unrealized_pnl < 0.0 && self.unrealized_pnl.abs() > taker_fee * 10.0 {
            crate::info!(
                "stop loss: price: {}, position: {}, amout: {}, unrealized pnl: {}",
                price,
                self.position,
                self.position * price,
                self.unrealized_pnl
            );
            Some((price, self.position))
        } else {
            None
        }
    }
}
