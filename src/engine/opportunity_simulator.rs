use std::collections::HashMap;

use crate::{
    encoding::{
        futures_order::OrderSide,
        futures_order_response::{OrderStatus, OrderTradeUpdate},
    },
    engine::{
        arbitrage_engine_futures_utils::generate_take_profit_and_stop_loss_opportunity,
        futures_const::FUTURES_QUANTITY_TICK_SIZE, futures_position::FuturesPosition,
    },
    utils::perf::{circles_to_ns, system_now_in_us},
};

#[derive(PartialEq, Eq, PartialOrd, Ord, Clone, Copy, Debug)]
pub enum OpportunityStatus {
    Pending,
    Filled,
    Success,
    Fail,
    Canceled,
}

#[derive(PartialEq, Eq, PartialOrd, Ord, <PERSON>lone, Co<PERSON>, Debug)]
pub enum PredictResult {
    None,
    Win,
    Loss,
}

#[derive(PartialEq, Eq, PartialOrd, Or<PERSON>, <PERSON><PERSON>, Co<PERSON>, Debug)]
pub enum OpportunityType {
    LargeTrade,
    OrderbookImbalance,
    TakeProfit,
    StopLoss,
}

impl OpportunityType {
    pub fn should_cal_win_or_loss(&self) -> bool {
        match self {
            OpportunityType::LargeTrade => true,
            OpportunityType::OrderbookImbalance => true,
            OpportunityType::TakeProfit => false,
            OpportunityType::StopLoss => false,
        }
    }
}

#[derive(Clone)]
pub struct Opportunity {
    pub id: u64,
    pub origin_order_id: u64,
    pub price: f64,
    pub side: OrderSide,
    pub status: OpportunityStatus,
    pub predict_result: PredictResult,
    pub opportunity_type: OpportunityType,
}

pub struct OpportunitySimulator {
    pub opportunities: HashMap<u64, Opportunity>,
    pub take_profit_opportunities: HashMap<u64, [Opportunity; 2]>,
    pub total_count: usize,
    pub pending_count: usize,
    pub filled_count: usize,
    pub succuess_count: usize, // 成功take profit
    pub win_count: usize,      // 预测成功
    pub loss_count: usize,     // 预测失败
    pub win_count_large_trade: usize,
    pub win_count_orderbook_imbalance: usize,
    pub loss_count_large_trade: usize,
    pub loss_count_orderbook_imbalance: usize,
    pub positions: FuturesPosition,
    pub avg_fill_latency: f64,
}

impl OpportunitySimulator {
    pub fn new() -> Self {
        Self {
            opportunities: HashMap::new(),
            take_profit_opportunities: HashMap::new(),
            total_count: 0,
            pending_count: 0,
            succuess_count: 0,
            win_count: 0,
            loss_count: 0,
            win_count_large_trade: 0,
            win_count_orderbook_imbalance: 0,
            loss_count_large_trade: 0,
            loss_count_orderbook_imbalance: 0,
            positions: FuturesPosition::new(),
            filled_count: 0,
            avg_fill_latency: 0.0,
        }
    }

    pub fn add_opportunity(&mut self, opportunity: Opportunity) {
        self.opportunities.insert(opportunity.id, opportunity);
        self.total_count += 1;
        self.pending_count += 1;
    }

    // pub fn add_take_profit_opportunity(&mut self, opportunity: [Opportunity; 2]) {
    //     self.take_profit_opportunities
    //         .insert(opportunity.id, opportunity);
    // }

    pub fn add_trade(&mut self, trade: &OrderTradeUpdate) {
        let order_id = trade.order_id;
        if self.opportunities.contains_key(&order_id) {
            self.opportunities.get_mut(&order_id).unwrap().status = OpportunityStatus::Filled;
        } else if self.take_profit_opportunities.contains_key(&order_id) {
            self.take_profit_opportunities
                .get_mut(&order_id)
                .unwrap()
                .status = OpportunityStatus::Filled;
            self.pending_count -= 1;
            self.succuess_count += 1;
        }
    }

    fn can_fill(opportunity: &Opportunity, best_bid: f64, best_ask: f64) -> bool {
        (opportunity.side == OrderSide::Buy && opportunity.price >= best_ask)
            || (opportunity.side == OrderSide::Sell && opportunity.price <= best_bid)
    }

    pub fn update_price(&mut self, best_bid: f64, best_ask: f64) {
        let mut has_filled = false;
        let now = system_now_in_us();
        for opportunity in self.opportunities.iter_mut() {
            self.update_stats(now, opportunity.0, opportunity.1, best_bid, best_ask);
            if opportunity.1.status == OpportunityStatus::Pending {
                if Self::can_fill(opportunity.1, best_bid, best_ask) {
                    let price = opportunity.1.price;
                    let side = opportunity.1.side;
                    opportunity.1.status = OpportunityStatus::Filled;
                    self.avg_fill_latency = ((now - opportunity.0) as f64
                        + (self.filled_count as f64 * self.avg_fill_latency))
                        / (self.filled_count + 1) as f64;
                    self.filled_count += 1;
                    let opportunities = generate_take_profit_and_stop_loss_opportunity(
                        price,
                        opportunity.1.side,
                        *opportunity.0,
                    );
                    self.take_profit_opportunities
                        .insert(opportunities[0].id, opportunities);
                    let trade = OrderTradeUpdate {
                        order_id: opportunity.1.id,
                        order_status: OrderStatus::Filled,
                        order_side: side,
                        price,
                        quantity: FUTURES_QUANTITY_TICK_SIZE,
                    };
                    self.positions.add_trade(&trade);
                    has_filled = true;
                }
            }
        }
        for opportunity in self.take_profit_opportunities.iter_mut() {
            if opportunity.1[0].status == OpportunityStatus::Pending {
                let (filled_index, canceled_index) =
                    if Self::can_fill(&opportunity.1[0], best_bid, best_ask) {
                        (0, 1)
                    } else if Self::can_fill(&opportunity.1[1], best_bid, best_ask) {
                        (1, 0)
                    } else {
                        continue;
                    };
                opportunity.1[filled_index].status = OpportunityStatus::Success;
                opportunity.1[canceled_index].status = OpportunityStatus::Canceled;
                self.pending_count -= 1;
                if filled_index == 0 {
                    self.succuess_count += 1;
                }
                let origin_opportunity = self
                    .opportunities
                    .get(&opportunity.1[filled_index].origin_order_id)
                    .unwrap();
                crate::info!(
                    r#"
filled take profit opportunity: side: {} price: {} best_bid: {} best_ask:{}
origin opportunity: side: {} price: {}
"#,
                    opportunity.1[filled_index].side.to_string(),
                    opportunity.1[filled_index].price,
                    best_bid,
                    best_ask,
                    origin_opportunity.side.to_string(),
                    origin_opportunity.price,
                );
                let trade = OrderTradeUpdate {
                    order_id: opportunity.1[0].id,
                    order_status: OrderStatus::Filled,
                    order_side: opportunity.1[0].side,
                    price: opportunity.1[0].price,
                    quantity: FUTURES_QUANTITY_TICK_SIZE,
                };
                self.positions.add_trade(&trade);
                has_filled = true;
            }
        }
        self.positions.update_current_price(best_bid, best_ask);
        if has_filled {
            self.log_stats();
        }
    }

    fn update_stats(
        &mut self,
        now: u64,
        opportunity_time: &u64,
        opportunity: &mut Opportunity,
        best_bid: f64,
        best_ask: f64,
    ) {
        if opportunity.predict_result == PredictResult::None
            && opportunity.opportunity_type.should_cal_win_or_loss()
            && circles_to_ns(now - opportunity_time) > 2_000_000.0
        {
            opportunity.predict_result = match opportunity.side {
                OrderSide::Buy => match opportunity.price < best_bid {
                    true => PredictResult::Win,
                    false => PredictResult::Loss,
                },
                OrderSide::Sell => match opportunity.price > best_ask {
                    true => PredictResult::Win,
                    false => PredictResult::Loss,
                },
            };
            match opportunity.predict_result {
                PredictResult::Win => match opportunity.opportunity_type {
                    OpportunityType::LargeTrade => {
                        self.win_count += 1;
                        self.win_count_large_trade += 1;
                    }
                    OpportunityType::OrderbookImbalance => {
                        self.win_count += 1;
                        self.win_count_orderbook_imbalance += 1;
                    }
                    _ => {}
                },
                PredictResult::Loss => match opportunity.opportunity_type {
                    OpportunityType::LargeTrade => {
                        self.loss_count += 1;
                        self.loss_count_large_trade += 1;
                    }
                    OpportunityType::OrderbookImbalance => {
                        self.loss_count += 1;
                        self.loss_count_orderbook_imbalance += 1;
                    }
                    _ => {}
                },
                PredictResult::None => {}
            };
        }
    }

    fn log_stats(&self) {
        crate::info!(
            r#"
Total_count: {}
Pending_count: {}
Success_count: {}
Win_count: {}
Loss_count: {}
Win_rate: {:.2}%
Win_count_large_trade: {}
Win_count_orderbook_imbalance: {}
Loss_count_large_trade: {}
Loss_count_orderbook_imbalance: {}
Avg_fill_latency: {:.2}ms
Realized pnl: {:.5}
Unrealized pnl: {:.5}
Success_rate: {:.3}%
Current position: {:.3}
Entry price: {:.2}
"#,
            self.total_count,
            self.pending_count,
            self.succuess_count,
            self.win_count,
            self.loss_count,
            (self.win_count as f64 / (self.win_count + self.loss_count) as f64) * 100.0,
            self.win_count_large_trade,
            self.win_count_orderbook_imbalance,
            self.loss_count_large_trade,
            self.loss_count_orderbook_imbalance,
            self.avg_fill_latency / 1000.0,
            self.positions.realized_pnl,
            self.positions.unrealized_pnl,
            (self.succuess_count as f64 / self.total_count as f64) * 100.0,
            self.positions.position,
            self.positions.entry_price,
        );
        crate::info!(
            "Unfilled orders: {}",
            self.opportunities
                .iter()
                .filter(|o| o.1.status == OpportunityStatus::Pending)
                .count()
        );
        let unfilled_take_profit_count = self
            .take_profit_opportunities
            .iter()
            .filter(|o| o.1[0].status == OpportunityStatus::Pending)
            .count();
        crate::info!(
            "Unfilled take profit orders count: {}",
            unfilled_take_profit_count
        );
    }
}
